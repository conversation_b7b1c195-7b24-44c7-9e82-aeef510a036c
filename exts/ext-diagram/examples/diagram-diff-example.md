# Diagram Diff Example

This example demonstrates how to use the diagram diff functionality to compare AI-generated code structures before and after changes.

## Usage

The `DiagramDiffAnalyzer` can be used to compare two `GraphvizDiagramData` structures and identify:
- Added classes/entities
- Removed classes/entities  
- Added fields/methods
- Removed fields/methods

## Example Code

```kotlin
import cc.unitmesh.diagram.diff.DiagramDiffAnalyzer
import cc.unitmesh.diagram.model.*

// Create analyzer
val analyzer = DiagramDiffAnalyzer()

// Old diagram (before AI changes)
val oldDiagram = GraphvizDiagramData(
    nodes = emptyList(),
    entities = listOf(
        GraphvizEntityNodeData("User", listOf(
            GraphvizNodeField("id", "String", false, ChangeStatus.UNCHANGED, isMethodField = false),
            GraphvizNodeField("name", "String", false, ChangeStatus.UNCHANGED, isMethodField = false),
            GraphvizNodeField("email", "String", false, ChangeStatus.UNCHANGED, isMethodField = false)
        ))
    ),
    edges = emptyList()
)

// New diagram (after AI changes)
val newDiagram = GraphvizDiagramData(
    nodes = emptyList(),
    entities = listOf(
        GraphvizEntityNodeData("User", listOf(
            GraphvizNodeField("id", "String", false, ChangeStatus.UNCHANGED, isMethodField = false),
            GraphvizNodeField("name", "String", false, ChangeStatus.UNCHANGED, isMethodField = false),
            // email field removed
            GraphvizNodeField("phone", "String", false, ChangeStatus.UNCHANGED, isMethodField = false), // New field
            GraphvizNodeField("save()", "void", false, ChangeStatus.UNCHANGED, isMethodField = true) // New method
        )),
        GraphvizEntityNodeData("Product", listOf( // New entity
            GraphvizNodeField("name", "String", false, ChangeStatus.UNCHANGED, isMethodField = false),
            GraphvizNodeField("price", "Double", false, ChangeStatus.UNCHANGED, isMethodField = false)
        ))
    ),
    edges = emptyList()
)

// Analyze differences
val diffResult = analyzer.analyzeDiff(oldDiagram, newDiagram)

// The result will have change status annotations:
// - User.phone field will be marked as ADDED
// - User.save() method will be marked as ADDED  
// - User.email field will be marked as REMOVED
// - Product entity and all its fields will be marked as ADDED
```

## Visual Representation

In the diagram display:
- **Added elements** are shown with `+` prefix and bold text
- **Removed elements** are shown with `-` prefix and grayed out text
- **Unchanged elements** are shown normally

### Example Mermaid Output

```mermaid
classDiagram
    class User {
        id: String
        name: String
        - email: String
        + phone: String
        + save(): void
    }
    
    class Product {
        + name: String
        + price: Double
    }
```

## Categories in Diagram View

The diagram categorizes elements into:
- **Fields** - Regular unchanged fields
- **Attributes** - Node attributes
- **Added Fields** - New fields (shown with + icon)
- **Removed Fields** - Deleted fields (shown with - icon)
- **Added Methods** - New methods (shown with + icon)
- **Removed Methods** - Deleted methods (shown with - icon)

This allows users to easily filter and view specific types of changes in the diagram interface.
