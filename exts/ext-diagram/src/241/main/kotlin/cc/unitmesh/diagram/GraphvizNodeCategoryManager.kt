package cc.unitmesh.diagram

import com.intellij.diagram.AbstractDiagramNodeContentManager
import com.intellij.diagram.DiagramBuilder
import com.intellij.diagram.DiagramCategory
import com.intellij.util.PlatformIcons
import cc.unitmesh.diagram.model.GraphvizNodeField
import cc.unitmesh.diagram.model.GraphvizAttributeItem
import cc.unitmesh.diagram.model.ChangeStatus
import com.intellij.icons.AllIcons

/**
 * Node content manager for Graphviz diagrams
 * Similar to JdlUmlCategoryManager in JHipster UML implementation
 */
class GraphvizNodeCategoryManager : AbstractDiagramNodeContentManager() {

    companion object Companion {
        private val FIELDS_CATEGORY = DiagramCategory(
            "Fields",
            AllIcons.Nodes.Field,
            true,
            false
        )

        private val ATTRIBUTES_CATEGORY = DiagramCategory(
            "Attributes",
            AllIcons.Nodes.Method,
            true,
            false
        )

        private val ADDED_FIELDS_CATEGORY = DiagramCategory(
            "Added Fields",
            AllIcons.General.Add,
            true,
            false
        )

        private val REMOVED_FIELDS_CATEGORY = DiagramCategory(
            "Removed Fields",
            AllIcons.General.Remove,
            true,
            false
        )

        private val ADDED_METHODS_CATEGORY = DiagramCategory(
            "Added Methods",
            AllIcons.General.Add,
            true,
            false
        )

        private val REMOVED_METHODS_CATEGORY = DiagramCategory(
            "Removed Methods",
            AllIcons.General.Remove,
            true,
            false
        )
    }

    override fun getContentCategories(): Array<DiagramCategory> {
        return arrayOf(
            FIELDS_CATEGORY,
            ATTRIBUTES_CATEGORY,
            ADDED_FIELDS_CATEGORY,
            REMOVED_FIELDS_CATEGORY,
            ADDED_METHODS_CATEGORY,
            REMOVED_METHODS_CATEGORY
        )
    }

    override fun isInCategory(
        nodeElement: Any?,
        item: Any?,
        category: DiagramCategory,
        builder: DiagramBuilder?
    ): Boolean {
        return when (item) {
            is GraphvizNodeField -> {
                when (category) {
                    FIELDS_CATEGORY -> !item.isMethod() && item.isUnchanged()
                    ADDED_FIELDS_CATEGORY -> !item.isMethod() && item.isAdded()
                    REMOVED_FIELDS_CATEGORY -> !item.isMethod() && item.isRemoved()
                    ADDED_METHODS_CATEGORY -> item.isMethod() && item.isAdded()
                    REMOVED_METHODS_CATEGORY -> item.isMethod() && item.isRemoved()
                    else -> false
                }
            }
            is GraphvizAttributeItem -> category == ATTRIBUTES_CATEGORY
            else -> super.isInCategory(nodeElement, item, category, builder)
        }
    }
}
